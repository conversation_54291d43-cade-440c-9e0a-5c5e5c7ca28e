'use client'

import { Breadcrumb as ChakraBreadcrumb, Text } from '@chakra-ui/react'
import Link from 'next/link'

export interface BreadcrumbItem {
  label: string
  href?: string
  isCurrentPage?: boolean
}

interface BreadcrumbProps {
  items: BreadcrumbItem[]
}

const Breadcrumb: React.FC<BreadcrumbProps> = ({
  items
}) => {
  return (
    <ChakraBreadcrumb.Root whiteSpace={"nowrap"}>
      <ChakraBreadcrumb.List>
        {items.map((item, index) => (
          <ChakraBreadcrumb.Item>
            {item.isCurrentPage ? (
              <ChakraBreadcrumb.CurrentLink>
                <Text lineClamp={1} maxW={"600px"} fontSize="sm" fontWeight="bold" color="gray.600">
                  {item.label}
                </Text>
              </ChakraBreadcrumb.CurrentLink>
            ) : (
              <ChakraBreadcrumb.Link href={item.href}>{item.label}</ChakraBreadcrumb.Link>
            )}
            {index < items.length - 1 && (
              <ChakraBreadcrumb.Separator />
            )}
          </ChakraBreadcrumb.Item>
        ))}
      </ChakraBreadcrumb.List>
    </ChakraBreadcrumb.Root >
  )
}

export default Breadcrumb
