'use client'

import React, { useState, useRef } from 'react'
import {
  Dialog,
  Portal,
  CloseButton,
  Box,
  Image,
  IconButton,
} from '@chakra-ui/react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Zoom, Pagination } from 'swiper/modules'
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa'
import SwiperCore from 'swiper'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/zoom'
import 'swiper/css/pagination'

interface ImageZoomModalProps {
  images: string[]
  initialSlide?: number
  isOpen: boolean
  onClose: () => void
  productTitle?: string
}

const ImageZoomModal: React.FC<ImageZoomModalProps> = ({
  images,
  initialSlide = 0,
  isOpen,
  onClose,
  productTitle = 'Product Image'
}) => {
  const swiperRef = useRef<SwiperCore>(null)
  const [currentSlide, setCurrentSlide] = useState(initialSlide)

  const handlePrevSlide = () => {
    swiperRef.current?.slidePrev()
  }

  const handleNextSlide = () => {
    swiperRef.current?.slideNext()
  }

  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={(e) => !e.open && onClose()}
      size="full"
      motionPreset="slide-in-bottom"
    >
      <Portal>
        <Dialog.Backdrop bg="blackAlpha.900" />
        <Dialog.Positioner>
          <Dialog.Content bg="black" m={0} borderRadius={0}>
            <Dialog.CloseTrigger asChild>
              <CloseButton
                position="fixed"
                top={4}
                right={4}
                zIndex={10}
                color="white"
                bg="blackAlpha.600"
                _hover={{ bg: 'blackAlpha.800' }}
                size="lg"
              />
            </Dialog.CloseTrigger>

            <Dialog.Body p={0} position="relative" h="100vh" display="flex" alignItems="center">
              {/* Navigation Buttons */}
              {images.length > 1 && (
                <>
                  <IconButton
                    aria-label="Previous image"
                    position="absolute"
                    left={4}
                    top="50%"
                    transform="translateY(-50%)"
                    zIndex={10}
                    bg="blackAlpha.600"
                    color="white"
                    _hover={{ bg: 'blackAlpha.800' }}
                    size="lg"
                    onClick={handlePrevSlide}
                    disabled={currentSlide === 0}
                  >
                    <FaChevronLeft />
                  </IconButton>

                  <IconButton
                    aria-label="Next image"
                    position="absolute"
                    right={4}
                    top="50%"
                    transform="translateY(-50%)"
                    zIndex={10}
                    bg="blackAlpha.600"
                    color="white"
                    _hover={{ bg: 'blackAlpha.800' }}
                    size="lg"
                    onClick={handleNextSlide}
                    disabled={currentSlide === images.length - 1}
                  >
                    <FaChevronRight />
                  </IconButton>
                </>
              )}

              {/* Image Counter */}
              {images.length > 1 && (
                <Box
                  position="absolute"
                  bottom={4}
                  left="50%"
                  transform="translateX(-50%)"
                  zIndex={10}
                  bg="blackAlpha.600"
                  color="white"
                  px={3}
                  py={1}
                  borderRadius="md"
                  fontSize="sm"
                >
                  {currentSlide + 1} / {images.length}
                </Box>
              )}

              {/* Swiper */}
              <Swiper
                modules={[Navigation, Zoom, Pagination]}
                spaceBetween={0}
                slidesPerView={1}
                zoom={{
                  maxRatio: 3,
                  minRatio: 1,
                }}
                initialSlide={initialSlide}
                onSwiper={(swiper) => (swiperRef.current = swiper)}
                onSlideChange={(swiper) => setCurrentSlide(swiper.activeIndex)}
                style={{ width: '100%', height: '100%' }}
              >
                {images.map((image, index) => (
                  <SwiperSlide key={index}>
                    <div className="swiper-zoom-container">
                      <Image
                        src={image}
                        alt={`${productTitle} - Image ${index + 1}`}
                        objectFit="contain"
                        w="100%"
                        h="100%"
                        maxH="100vh"
                        draggable={false}
                      />
                    </div>
                  </SwiperSlide>
                ))}
              </Swiper>
            </Dialog.Body>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  )
}

export default ImageZoomModal
