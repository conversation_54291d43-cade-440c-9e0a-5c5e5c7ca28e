'use client'

import React, { useState, useRef, useEffect } from 'react'
import {
  Dialog,
  Portal,
  CloseButton,
  Box,
  Image,
  IconButton,
  Text,
  HStack,
  VStack,
} from '@chakra-ui/react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Zoom, Thumbs, FreeMode } from 'swiper/modules'
import { FaChevronLeft, FaChevronRight } from 'react-icons/fa'
import SwiperCore from 'swiper'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/zoom'
import 'swiper/css/thumbs'
import 'swiper/css/free-mode'

interface ImageZoomModalProps {
  images: string[]
  initialSlide?: number
  isOpen: boolean
  onClose: () => void
  productTitle?: string
}

const ImageZoomModal: React.FC<ImageZoomModalProps> = ({
  images,
  initialSlide = 0,
  isOpen,
  onClose,
  productTitle = 'Product Image'
}) => {
  const swiperRef = useRef<SwiperCore>(null)
  const [currentSlide, setCurrentSlide] = useState(initialSlide)
  const [isZoomed, setIsZoomed] = useState(false)

  // Update current slide when initialSlide changes
  useEffect(() => {
    setCurrentSlide(initialSlide)
    if (swiperRef.current) {
      swiperRef.current.slideTo(initialSlide)
    }
  }, [initialSlide])

  // Reset zoom when slide changes
  useEffect(() => {
    setIsZoomed(false)
  }, [currentSlide])

  const handlePrevSlide = () => {
    swiperRef.current?.slidePrev()
  }

  const handleNextSlide = () => {
    swiperRef.current?.slideNext()
  }

  const handleImageClick = () => {
    setIsZoomed(!isZoomed)
  }

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  return (
    <Dialog.Root
      open={isOpen}
      onOpenChange={(e) => !e.open && onClose()}
      size="full"
      motionPreset="slide-in-bottom"
    >
      <Portal>
        <Dialog.Backdrop bg="blackAlpha.950" onClick={handleBackdropClick} />
        <Dialog.Positioner>
          <Dialog.Content bg="black" m={0} borderRadius={0} position="relative">
            {/* Header with close button and image counter */}
            <Box
              position="fixed"
              top={0}
              left={0}
              right={0}
              zIndex={20}
              bg="blackAlpha.800"
              backdropFilter="blur(10px)"
              p={4}
              display="flex"
              justifyContent="space-between"
              alignItems="center"
            >
              <Text color="white" fontSize="lg" fontWeight="medium">
                {productTitle}
              </Text>
              <HStack gap={4}>
                {images.length > 1 && (
                  <Text color="white" fontSize="sm">
                    {currentSlide + 1} / {images.length}
                  </Text>
                )}
                <Dialog.CloseTrigger asChild>
                  <CloseButton
                    color="white"
                    bg="blackAlpha.600"
                    _hover={{ bg: 'blackAlpha.800' }}
                    size="lg"
                  />
                </Dialog.CloseTrigger>
              </HStack>
            </Box>

            {/* Main Content Area */}
            <Dialog.Body p={0} position="relative" h="100vh" display="flex" flexDirection="column">
              {/* Main Image Area */}
              <Box flex={1} position="relative" display="flex" alignItems="center" pt="80px" pb="120px">
                {/* Navigation Buttons */}
                {images.length > 1 && (
                  <>
                    <IconButton
                      aria-label="Previous image"
                      position="absolute"
                      left={4}
                      top="50%"
                      transform="translateY(-50%)"
                      zIndex={15}
                      bg="blackAlpha.700"
                      color="white"
                      _hover={{ bg: 'blackAlpha.900' }}
                      size="lg"
                      onClick={handlePrevSlide}
                      disabled={currentSlide === 0}
                      borderRadius="full"
                    >
                      <FaChevronLeft />
                    </IconButton>

                    <IconButton
                      aria-label="Next image"
                      position="absolute"
                      right={4}
                      top="50%"
                      transform="translateY(-50%)"
                      zIndex={15}
                      bg="blackAlpha.700"
                      color="white"
                      _hover={{ bg: 'blackAlpha.900' }}
                      size="lg"
                      onClick={handleNextSlide}
                      disabled={currentSlide === images.length - 1}
                      borderRadius="full"
                    >
                      <FaChevronRight />
                    </IconButton>
                  </>
                )}

                {/* Main Swiper */}
                <Swiper
                  modules={[Navigation, Zoom]}
                  spaceBetween={0}
                  slidesPerView={1}
                  zoom={{
                    maxRatio: 3,
                    minRatio: 1,
                    toggle: true,
                  }}
                  initialSlide={initialSlide}
                  onSwiper={(swiper) => (swiperRef.current = swiper)}
                  onSlideChange={(swiper) => setCurrentSlide(swiper.activeIndex)}
                  style={{ width: '100%', height: '100%' }}
                >
                  {images.map((image, index) => (
                    <SwiperSlide key={index}>
                      <div className="swiper-zoom-container">
                        <Image
                          src={image}
                          alt={`${productTitle} - Image ${index + 1}`}
                          objectFit="contain"
                          w="100%"
                          h="100%"
                          maxH="calc(100vh - 200px)"
                          draggable={false}
                          cursor="zoom-in"
                          onClick={handleImageClick}
                        />
                      </div>
                    </SwiperSlide>
                  ))}
                </Swiper>
              </Box>

              {/* Bottom Thumbnail Navigation */}
              {images.length > 1 && (
                <Box
                  position="fixed"
                  bottom={0}
                  left={0}
                  right={0}
                  zIndex={20}
                  bg="blackAlpha.800"
                  backdropFilter="blur(10px)"
                  p={4}
                >
                  <VStack gap={3}>
                    <Text color="white" fontSize="sm" textAlign="center">
                      Tap image to zoom • Swipe to navigate
                    </Text>
                    <Swiper
                      modules={[FreeMode, Thumbs]}
                      spaceBetween={8}
                      slidesPerView="auto"
                      freeMode={true}
                      watchSlidesProgress={true}
                      centeredSlides={true}
                      style={{ width: '100%', maxWidth: '400px' }}
                    >
                      {images.map((image, index) => (
                        <SwiperSlide key={index} style={{ width: 'auto' }}>
                          <Box
                            w="60px"
                            h="60px"
                            borderRadius="md"
                            overflow="hidden"
                            cursor="pointer"
                            border="2px solid"
                            borderColor={currentSlide === index ? 'white' : 'transparent'}
                            transition="border-color 0.2s"
                            onClick={() => {
                              setCurrentSlide(index)
                              swiperRef.current?.slideTo(index)
                            }}
                          >
                            <Image
                              src={image}
                              alt={`Thumbnail ${index + 1}`}
                              objectFit="cover"
                              w="100%"
                              h="100%"
                              draggable={false}
                            />
                          </Box>
                        </SwiperSlide>
                      ))}
                    </Swiper>
                  </VStack>
                </Box>
              )}
            </Dialog.Body>
          </Dialog.Content>
        </Dialog.Positioner>
      </Portal>
    </Dialog.Root>
  )
}

export default ImageZoomModal
