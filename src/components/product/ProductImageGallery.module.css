/* ProductImageGallery Styles */
.mainSwiper {
  width: 100%;
  height: 100%;
  display: block !important;
}

.mainSwiper .swiper-slide {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.thumbnailSwiper {
  width: 100%;
  height: auto;
  display: block !important;
}

.thumbnailSwiper .swiper-slide {
  width: auto !important;
  height: auto;
}

.imageContainer {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: zoom-in;
}

.imageContainer:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

.productImage {
  max-width: 90%;
  max-height: 90%;
  width: auto;
  height: auto;
  object-fit: contain;
  user-select: none;
  -webkit-user-drag: none;
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  user-select: none;
  -webkit-user-drag: none;
}

.thumbnailContainer {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid;
  transition: border-color 0.2s ease;
}

.thumbnailContainer:hover {
  border-color: #3182ce;
}

.thumbnailContainer.active {
  border-color: #3182ce;
}

.thumbnailContainer.inactive {
  border-color: #e2e8f0;
}
