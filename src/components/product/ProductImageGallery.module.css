/* ProductImageGallery Styles */
.mainSwiper {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  position: relative;
}

.mainSwiper .swiper-slide {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

.mainSwiper .swiper-wrapper {
  height: 100% !important;
}

.thumbnailSwiper {
  width: 100% !important;
  height: auto !important;
  display: block !important;
}

.thumbnailSwiper .swiper-slide {
  width: auto !important;
  height: auto !important;
  flex-shrink: 0 !important;
}

.imageContainer {
  width: 100% !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: zoom-in;
  position: relative;
}

.imageContainer:hover {
  transform: scale(1.02);
  transition: transform 0.2s ease;
}

.productImage {
  max-width: 90% !important;
  max-height: 90% !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  user-select: none;
  -webkit-user-drag: none;
  transition: opacity 0.3s ease;
}

.thumbnailImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  user-select: none;
  -webkit-user-drag: none;
}

.thumbnailContainer {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid;
  transition: border-color 0.2s ease;
}

.thumbnailContainer:hover {
  border-color: #3182ce;
}

.thumbnailContainer.active {
  border-color: #3182ce;
}

.thumbnailContainer.inactive {
  border-color: #e2e8f0;
}
