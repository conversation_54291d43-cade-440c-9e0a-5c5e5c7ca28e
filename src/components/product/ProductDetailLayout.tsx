'use client'

import { Box, Flex, BoxProps } from '@chakra-ui/react'

interface ProductDetailLayoutProps extends BoxProps {
  leftContent: React.ReactNode
  rightContent: React.ReactNode
  leftProps?: BoxProps
  rightProps?: BoxProps
  containerProps?: BoxProps
}

const ProductDetailLayout: React.FC<ProductDetailLayoutProps> = ({
  leftContent,
  rightContent,
  leftProps = {},
  rightProps = {},
  containerProps = {},
  ...boxProps
}) => {
  return (
    <Box {...boxProps}>
      <Box mx="auto" {...containerProps}>
        <Flex
          align="flex-start"
          mx="auto"
          gap={4}
          direction={{ base: 'column', lg: 'row' }}
        >
          <Box
            flex={1}
            px={{ base: 4, md: 6 }}
            alignSelf="flex-start"
            height={{
              base: 'auto',
              md: '650px',
            }}
            w={{ base: '100%', lg: 'auto' }}
            {...leftProps}
          >
            {leftContent}
          </Box>
          
          {/* Right Content - Product Info and Bidding */}
          <Box
            flex={1}
            alignSelf="flex-start"
            bg="white"
            px={{ base: 4, md: 8, lg: 20 }}
            pt={{ base: 8, md: 12, lg: 16 }}
            pb={{ base: 8, md: 12 }}
            borderRadius={{ base: 'md', lg: 'none' }}
            borderTopRightRadius={{ lg: 'sm' }}
            borderBottomRightRadius={{ lg: 'sm' }}
            position={{ lg: 'sticky' }}
            top={{ lg: 0 }}
            w={{ base: '100%', lg: 'auto' }}
            {...rightProps}
          >
            {rightContent}
          </Box>
        </Flex>
      </Box>
    </Box>
  )
}

export default ProductDetailLayout
