'use client'

import React, { useState, useRef, useEffect } from 'react'
import {
  Box,
  Button,
  Icon,
  Image,
  Text,
  VStack,
  useDisclosure,
  IconButton,
  Skeleton,
} from '@chakra-ui/react'
import { Swiper, SwiperSlide } from 'swiper/react'
import { Navigation, Thumbs, FreeMode } from 'swiper/modules'
import { FaRegHeart, FaChevronLeft, FaChevronRight } from 'react-icons/fa'
import { ProductItem } from '@/types/product'
import ImageZoomModal from './ImageZoomModal'
import SwiperCore from 'swiper'
import styles from './ProductImageGallery.module.css'

// Import Swiper styles
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/thumbs'
import 'swiper/css/free-mode'

interface ProductImageGalleryProps {
  item: ProductItem
  backgroundColorCardImage?: string
  containerProps?: React.ComponentProps<typeof Box>
  boxSizeWatchList?: number
}

const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({
  item,
  backgroundColorCardImage = 'gray.100',
  containerProps = {},
  boxSizeWatchList = 4.5,
}) => {
  const [thumbsSwiper, setThumbsSwiper] = useState<SwiperCore | null>(null)
  const [activeIndex, setActiveIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [loadedImages, setLoadedImages] = useState<Set<number>>(new Set())
  const mainSwiperRef = useRef<SwiperCore>(null)
  const { open, onOpen, onClose } = useDisclosure()

  // Get images array, fallback to single image if images array doesn't exist
  const images = item.images && item.images.length > 0 ? item.images : [item.image]

  // Handle image loading
  const handleImageLoad = (index: number) => {
    setLoadedImages(prev => {
      const newSet = new Set(prev)
      newSet.add(index)
      return newSet
    })
  }

  // Check if all images are loaded
  useEffect(() => {
    if (loadedImages.size === images.length && images.length > 0) {
      setIsLoading(false)
      // Force update swipers after loading
      setTimeout(() => {
        if (mainSwiperRef.current) {
          mainSwiperRef.current.update()
        }
        if (thumbsSwiper) {
          thumbsSwiper.update()
        }
      }, 100)
    }
  }, [loadedImages.size, images.length, thumbsSwiper])

  // Reset loading state when images change
  useEffect(() => {
    setIsLoading(true)
    setLoadedImages(new Set())
    setActiveIndex(0)
  }, [item.id])

  // Force update on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      if (mainSwiperRef.current) {
        mainSwiperRef.current.update()
      }
    }, 500)
    return () => clearTimeout(timer)
  }, [])

  const handleThumbnailClick = (index: number) => {
    setActiveIndex(index)
    if (mainSwiperRef.current) {
      mainSwiperRef.current.slideTo(index)
    }
  }

  const handleMainImageClick = () => {
    onOpen()
  }

  const handlePrevSlide = () => {
    if (mainSwiperRef.current) {
      mainSwiperRef.current.slidePrev()
    }
  }

  const handleNextSlide = () => {
    if (mainSwiperRef.current) {
      mainSwiperRef.current.slideNext()
    }
  }

  return (
    <>
      <Box {...containerProps}>
        <VStack gap={4} align="stretch">
          {/* Main Image Swiper */}
          <Box
            position="relative"
            bg={backgroundColorCardImage}
            borderRadius="lg"
            overflow="hidden"
            cursor="zoom-in"
            w="100%"
            h={{ base: '300px', md: '400px', lg: '500px', xl: '600px' }}
            minH={{ base: '300px', md: '400px' }}
          >
          {/* Wishlist Button */}
          <Box
            position="absolute"
            top={3}
            right={3}
            zIndex={2}
          >
            <Button
              variant="ghost"
              color="gray.600"
              p={1}
              minW="auto"
              h="auto"
              display="flex"
              flexDirection="column"
              gap={0}
              bg="whiteAlpha.800"
              _hover={{ bg: 'whiteAlpha.900' }}
            >
              <Icon
                as={FaRegHeart}
                boxSize={boxSizeWatchList}
                color="gray.600"
                _hover={{ color: 'red.500' }}
                transition="color 0.2s"
              />
              <Text fontSize="xs">24</Text>
            </Button>
          </Box>

          {/* Navigation Arrows for Main Image */}
          {images.length > 1 && (
            <>
              <IconButton
                aria-label="Previous image"
                position="absolute"
                left={2}
                top="50%"
                transform="translateY(-50%)"
                zIndex={2}
                bg="whiteAlpha.800"
                color="gray.700"
                _hover={{ bg: 'whiteAlpha.900' }}
                size="sm"
                onClick={handlePrevSlide}
                opacity={activeIndex === 0 ? 0.5 : 1}
                disabled={activeIndex === 0}
              >
                <FaChevronLeft />
              </IconButton>

              <IconButton
                aria-label="Next image"
                position="absolute"
                right={2}
                top="50%"
                transform="translateY(-50%)"
                zIndex={2}
                bg="whiteAlpha.800"
                color="gray.700"
                _hover={{ bg: 'whiteAlpha.900' }}
                size="sm"
                onClick={handleNextSlide}
                opacity={activeIndex === images.length - 1 ? 0.5 : 1}
                disabled={activeIndex === images.length - 1}
              >
                <FaChevronRight />
              </IconButton>
            </>
          )}

          {/* Loading Skeleton */}
          {isLoading && (
            <Skeleton
              position="absolute"
              top={0}
              left={0}
              right={0}
              bottom={0}
              zIndex={1}
              borderRadius="lg"
            />
          )}

          {/* Main Image Swiper */}
          <Swiper
            modules={[Navigation, Thumbs]}
            spaceBetween={0}
            slidesPerView={1}
            thumbs={{ swiper: thumbsSwiper && !thumbsSwiper.destroyed ? thumbsSwiper : null }}
            onSwiper={(swiper) => {
              mainSwiperRef.current = swiper
              // Force update after swiper is ready
              setTimeout(() => {
                swiper.update()
              }, 100)
            }}
            onSlideChange={(swiper) => setActiveIndex(swiper.activeIndex)}
            className={styles.mainSwiper}
            style={{ opacity: isLoading ? 0 : 1, transition: 'opacity 0.3s ease' }}
          >
            {images.map((image, index) => (
              <SwiperSlide key={`${item.id}-${index}`}>
                <Box
                  className={styles.imageContainer}
                  p={{ base: 4, md: 6 }}
                  onClick={handleMainImageClick}
                >
                  <Image
                    src={image}
                    alt={`${item.title} - Image ${index + 1}`}
                    className={styles.productImage}
                    draggable={false}
                    onLoad={() => handleImageLoad(index)}
                    onError={() => handleImageLoad(index)} // Also handle errors to prevent infinite loading
                  />
                </Box>
              </SwiperSlide>
            ))}
          </Swiper>

          {/* Image Counter */}
          {images.length > 1 && (
            <Box
              position="absolute"
              bottom={3}
              right={3}
              zIndex={2}
              bg="blackAlpha.600"
              color="white"
              px={2}
              py={1}
              borderRadius="md"
              fontSize="xs"
            >
              {activeIndex + 1}/{images.length}
            </Box>
          )}
        </Box>

        {/* Thumbnail Swiper */}
        {images.length > 1 && (
          <Box w="100%" opacity={isLoading ? 0.5 : 1} transition="opacity 0.3s ease">
            <Swiper
              modules={[FreeMode, Navigation, Thumbs]}
              onSwiper={(swiper) => {
                setThumbsSwiper(swiper)
                // Force update after swiper is ready
                setTimeout(() => {
                  swiper.update()
                }, 100)
              }}
              spaceBetween={8}
              slidesPerView="auto"
              freeMode={true}
              watchSlidesProgress={true}
              className={styles.thumbnailSwiper}
            >
              {images.map((image, index) => (
                <SwiperSlide key={`${item.id}-thumb-${index}`} style={{ width: 'auto' }}>
                  <Box
                    w={{ base: '60px', md: '80px' }}
                    h={{ base: '60px', md: '80px' }}
                    className={`${styles.thumbnailContainer} ${
                      activeIndex === index ? styles.active : styles.inactive
                    }`}
                    onClick={() => handleThumbnailClick(index)}
                    position="relative"
                  >
                    {!loadedImages.has(index) && (
                      <Skeleton
                        position="absolute"
                        top={0}
                        left={0}
                        right={0}
                        bottom={0}
                        borderRadius="md"
                      />
                    )}
                    <Image
                      src={image}
                      alt={`${item.title} - Thumbnail ${index + 1}`}
                      className={styles.thumbnailImage}
                      style={{ opacity: loadedImages.has(index) ? 1 : 0 }}
                    />
                  </Box>
                </SwiperSlide>
              ))}
            </Swiper>
          </Box>
        )}
        </VStack>
      </Box>

      {/* Zoom Modal */}
      <ImageZoomModal
        images={images}
        initialSlide={activeIndex}
        isOpen={open}
        onClose={onClose}
        productTitle={item.title}
      />
    </>
  )
}

export default ProductImageGallery
