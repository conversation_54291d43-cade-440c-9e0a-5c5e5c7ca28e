'use client'
import React, { useState } from 'react';
import {
    <PERSON><PERSON>,
    HS<PERSON>ck,
    Icon,
    Text,
    Spinner
} from '@chakra-ui/react';
import { FaGoogle } from 'react-icons/fa';
// import { useAuth } from '@/hooks/useAuth';
import { toaster } from '../ui/toaster';

interface GoogleSignInButtonProps {
    variant?: 'solid' | 'outline' | 'ghost';
    size?: 'sm' | 'md' | 'lg';
    width?: string;
    isDisabled?: boolean;
    onSuccess?: () => void;
    onError?: (error: Error) => void;
}

const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({
    variant = 'outline',
    size = 'lg',
    width = '100%',
    isDisabled = false,
    onSuccess,
    onError
}) => {
    const [isLoading, setIsLoading] = useState(false);
    // const { signInWithGoogle } = useAuth();

    const handleGoogleSignIn = async () => {
        try {
            setIsLoading(true);
            // await signInWithGoogle();

            toaster.create({
                title: 'Success',
                description: 'Successfully signed in with Google',
                type: 'success',
                duration: 3000,
            })

            onSuccess?.();
        } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Failed to sign in with Google';

            toaster.create({
                title: 'Sign In Failed',
                description: errorMessage,
                type: 'error',
                duration: 5000,
            })

            onError?.(error instanceof Error ? error : new Error(errorMessage));
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Button
            variant={variant}
            size={size}
            width={width}
            onClick={handleGoogleSignIn}
            loading={isLoading}
            disabled={isDisabled || isLoading}
            loadingText="Signing in..."
            spinner={<Spinner size="sm" />}
            borderRadius="lg"
            borderColor={variant === 'outline' ? 'gray.300' : undefined}
            _hover={{
                borderColor: variant === 'outline' ? 'gray.400' : undefined,
                bg: variant === 'outline' ? 'gray.50' : undefined,
            }}
            _active={{
                bg: variant === 'outline' ? 'gray.100' : undefined,
            }}
        >
            <HStack gap={3}>
                <Icon
                    as={FaGoogle}
                    boxSize={size === 'lg' ? 5 : size === 'md' ? 4 : 3}
                />
                <Text
                    fontWeight="medium"
                    fontSize={size === 'lg' ? 'md' : size === 'md' ? 'sm' : 'xs'}
                >
                    Continue with Google
                </Text>
            </HStack>
        </Button>
    );
};

export default GoogleSignInButton;
